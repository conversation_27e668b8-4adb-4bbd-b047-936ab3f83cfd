// 简化的 Markdown 渲染器，专为 QML 设计
.pragma library

// 简化的 Markdown 解析和渲染函数
function renderMarkdown(text) {
    if (!text) return ""
    
    let html = text
    
    // 转义 HTML 特殊字符
    function escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;")
    }
    
    // 代码块处理 ```code```
    html = html.replace(/```([\s\S]*?)```/g, function(match, code) {
        return '<div style="background-color: #f6f8fa; border: 1px solid #e1e4e8; border-radius: 6px; padding: 12px; margin: 8px 0; font-family: \'Consolas\', \'Monaco\', monospace; white-space: pre-wrap; font-size: 13px;">' + 
               escapeHtml(code.trim()) + '</div>'
    })
    
    // 行内代码 `code`
    html = html.replace(/`([^`\n]+)`/g, function(match, code) {
        return '<span style="background-color: rgba(175, 184, 193, 0.2); border-radius: 3px; padding: 2px 4px; font-family: \'Consolas\', \'Monaco\', monospace; font-size: 13px;">' + 
               escapeHtml(code) + '</span>'
    })
    
    // 粗体 **text** 或 __text__
    html = html.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>')
    html = html.replace(/__(.*?)__/g, '<b>$1</b>')
    
    // 斜体 *text* 或 _text_
    html = html.replace(/\*([^*\n]+)\*/g, '<i>$1</i>')
    html = html.replace(/_([^_\n]+)_/g, '<i>$1</i>')
    
    // 删除线 ~~text~~
    html = html.replace(/~~(.*?)~~/g, '<s>$1</s>')
    
    // 链接 [text](url)
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, 
        '<a href="$2" style="color: #0366d6; text-decoration: none;">$1</a>')
    
    // 标题处理
    html = html.replace(/^######\s+(.*$)/gm, '<h6 style="margin: 12px 0 8px 0; font-size: 14px; font-weight: 600; color: #6a737d;">$1</h6>')
    html = html.replace(/^#####\s+(.*$)/gm, '<h5 style="margin: 14px 0 8px 0; font-size: 15px; font-weight: 600;">$1</h5>')
    html = html.replace(/^####\s+(.*$)/gm, '<h4 style="margin: 16px 0 8px 0; font-size: 16px; font-weight: 600;">$1</h4>')
    html = html.replace(/^###\s+(.*$)/gm, '<h3 style="margin: 18px 0 10px 0; font-size: 18px; font-weight: 600;">$1</h3>')
    html = html.replace(/^##\s+(.*$)/gm, '<h2 style="margin: 20px 0 10px 0; font-size: 20px; font-weight: 600;">$1</h2>')
    html = html.replace(/^#\s+(.*$)/gm, '<h1 style="margin: 24px 0 12px 0; font-size: 24px; font-weight: 600;">$1</h1>')
    
    // 引用块 > text
    html = html.replace(/^>\s+(.*$)/gm, function(match, quote) {
        return '<div style="border-left: 4px solid #dfe2e5; color: #6a737d; margin: 8px 0; padding: 0 16px;">' + quote + '</div>'
    })
    
    // 水平线 --- 或 ***
    html = html.replace(/^(---|\*\*\*|___)$/gm, '<hr style="border: none; border-top: 1px solid #e1e4e8; margin: 16px 0;">')
    
    // 无序列表 - 或 * 或 +
    let lines = html.split('\n')
    let inList = false
    let result = []
    
    for (let i = 0; i < lines.length; i++) {
        let line = lines[i]
        let listMatch = line.match(/^[\s]*[-*+]\s+(.*)$/)
        
        if (listMatch) {
            if (!inList) {
                result.push('<ul style="margin: 8px 0; padding-left: 20px;">')
                inList = true
            }
            result.push('<li style="margin: 2px 0;">' + listMatch[1] + '</li>')
        } else {
            if (inList) {
                result.push('</ul>')
                inList = false
            }
            result.push(line)
        }
    }
    
    if (inList) {
        result.push('</ul>')
    }
    
    html = result.join('\n')
    
    // 有序列表 1. text
    lines = html.split('\n')
    inList = false
    result = []
    
    for (let i = 0; i < lines.length; i++) {
        let line = lines[i]
        let listMatch = line.match(/^[\s]*\d+\.\s+(.*)$/)
        
        if (listMatch) {
            if (!inList) {
                result.push('<ol style="margin: 8px 0; padding-left: 20px;">')
                inList = true
            }
            result.push('<li style="margin: 2px 0;">' + listMatch[1] + '</li>')
        } else {
            if (inList) {
                result.push('</ol>')
                inList = false
            }
            result.push(line)
        }
    }
    
    if (inList) {
        result.push('</ol>')
    }
    
    html = result.join('\n')
    
    // 段落处理 - 将连续的非空行包装在 <p> 标签中
    lines = html.split('\n')
    result = []
    let inParagraph = false
    
    for (let i = 0; i < lines.length; i++) {
        let line = lines[i].trim()
        
        // 跳过已经是 HTML 标签的行
        if (line.match(/^<(h[1-6]|div|ul|ol|li|hr|blockquote)/)) {
            if (inParagraph) {
                result.push('</p>')
                inParagraph = false
            }
            result.push(lines[i])
        } else if (line === '') {
            if (inParagraph) {
                result.push('</p>')
                inParagraph = false
            }
            result.push('')
        } else {
            if (!inParagraph) {
                result.push('<p style="margin: 8px 0; line-height: 1.6;">')
                inParagraph = true
            }
            result.push(lines[i])
        }
    }
    
    if (inParagraph) {
        result.push('</p>')
    }
    
    return result.join('\n')
}
