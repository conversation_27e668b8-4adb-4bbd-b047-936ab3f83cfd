<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Renderer</title>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@14.0.0/dist/markdown-it.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: transparent;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
        
        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        
        h1 { font-size: 2em; border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; }
        h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 0.3em; }
        h3 { font-size: 1.25em; }
        h4 { font-size: 1em; }
        h5 { font-size: 0.875em; }
        h6 { font-size: 0.85em; color: #6a737d; }
        
        /* 段落和文本 */
        p {
            margin-top: 0;
            margin-bottom: 16px;
        }
        
        /* 列表 */
        ul, ol {
            margin-top: 0;
            margin-bottom: 16px;
            padding-left: 2em;
        }
        
        li {
            margin-bottom: 0.25em;
        }
        
        /* 代码块 */
        pre {
            background-color: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
            margin-bottom: 16px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        code {
            background-color: rgba(175, 184, 193, 0.2);
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        pre code {
            background-color: transparent;
            border: 0;
            display: inline;
            line-height: inherit;
            margin: 0;
            max-width: auto;
            overflow: visible;
            padding: 0;
            word-wrap: normal;
        }
        
        /* 引用 */
        blockquote {
            border-left: 0.25em solid #dfe2e5;
            color: #6a737d;
            margin: 0 0 16px 0;
            padding: 0 1em;
        }
        
        /* 表格 */
        table {
            border-collapse: collapse;
            border-spacing: 0;
            margin-bottom: 16px;
            width: 100%;
        }
        
        table th,
        table td {
            border: 1px solid #dfe2e5;
            padding: 6px 13px;
        }
        
        table th {
            background-color: #f6f8fa;
            font-weight: 600;
        }
        
        table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }
        
        /* 链接 */
        a {
            color: #0366d6;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        /* 水平线 */
        hr {
            background-color: #e1e4e8;
            border: 0;
            height: 0.25em;
            margin: 24px 0;
            padding: 0;
        }
        
        /* 强调 */
        strong {
            font-weight: 600;
        }
        
        /* 图片 */
        img {
            max-width: 100%;
            height: auto;
        }
        
        /* 任务列表 */
        .task-list-item {
            list-style-type: none;
        }
        
        .task-list-item input {
            margin: 0 0.2em 0.25em -1.6em;
            vertical-align: middle;
        }
        
        /* 数学公式支持 */
        .katex {
            font-size: 1.1em;
        }
    </style>
</head>
<body>
    <div id="content"></div>
    
    <script>
        // 初始化 markdown-it
        const md = window.markdownit({
            html: true,
            linkify: true,
            typographer: true,
            highlight: function (str, lang) {
                // 简单的代码高亮，不依赖外部库
                return '<pre><code class="language-' + (lang || 'text') + '">' +
                       md.utils.escapeHtml(str) + '</code></pre>';
            }
        });
        
        // 渲染 Markdown 的函数
        function renderMarkdown(markdownText) {
            try {
                const html = md.render(markdownText || '');
                document.getElementById('content').innerHTML = html;
                
                // 处理链接点击事件
                const links = document.querySelectorAll('a');
                links.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        // 通过 Qt WebChannel 发送链接点击事件
                        if (window.qtWebChannel && window.qtWebChannel.objects.messageHandler) {
                            window.qtWebChannel.objects.messageHandler.linkClicked(this.href);
                        } else {
                            // 备用方案：直接打开链接
                            window.open(this.href, '_blank');
                        }
                    });
                });
                
                // 代码块已经通过 markdown-it 处理，无需额外高亮
                
            } catch (error) {
                console.error('Markdown rendering error:', error);
                document.getElementById('content').innerHTML = '<p>渲染错误: ' + error.message + '</p>';
            }
        }
        
        // 暴露给 Qt 的接口
        window.setMarkdownContent = renderMarkdown;
        
        // 初始化时渲染空内容
        renderMarkdown('');
    </script>
</body>
</html>
