{"artifacts": [{"path": "QtMarkdown.exe"}, {"path": "QtMarkdown.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "add_dependencies", "_qt_internal_scan_qml_imports", "_qt_internal_generate_deploy_qml_imports_script", "cmake_language", "_qt_internal_finalize_executable", "qt6_finalize_target"], "files": ["C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6WebEngineQuick/Qt6WebEngineQuickTargets.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6WebEngineQuick/Qt6WebEngineQuickConfig.cmake", "build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/.qtc/package-manager/maintenance_tool_provider.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Quick/Qt6QuickTargets.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Quick/Qt6QuickConfig.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaTargets.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6QmlMeta/Qt6QmlMetaConfig.cmake", "C:/Qt/Tools/CMake_64/share/cmake-3.30/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Quick/Qt6QuickDependencies.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6WebEngineCore/Qt6WebEngineCoreTargets.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6WebEngineCore/Qt6WebEngineCoreConfig.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6WebEngineQuick/Qt6WebEngineQuickDependencies.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Qml/Qt6QmlTargets.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Qml/Qt6QmlConfig.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Qml/Qt6QmlDependencies.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "C:/Qt/6.8.3/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake:861:EVAL"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 21, "parent": 0}, {"command": 2, "file": 0, "line": 945, "parent": 1}, {"command": 1, "file": 0, "line": 646, "parent": 2}, {"command": 0, "file": 0, "line": 695, "parent": 3}, {"command": 4, "file": 1, "line": 26, "parent": 0}, {"command": 7, "file": 1, "line": 11, "parent": 0}, {"command": 7, "file": 4, "line": 297, "parent": 6}, {"file": 3, "parent": 7}, {"command": 6, "file": 3, "line": 55, "parent": 8}, {"file": 2, "parent": 9}, {"command": 5, "file": 2, "line": 61, "parent": 10}, {"command": 7, "file": 4, "line": 297, "parent": 6}, {"file": 6, "parent": 12}, {"command": 6, "file": 6, "line": 55, "parent": 13}, {"file": 5, "parent": 14}, {"command": 5, "file": 5, "line": 61, "parent": 15}, {"command": 6, "file": 6, "line": 43, "parent": 13}, {"file": 11, "parent": 17}, {"command": 9, "file": 11, "line": 45, "parent": 18}, {"command": 8, "file": 10, "line": 145, "parent": 19}, {"command": 7, "file": 9, "line": 76, "parent": 20}, {"command": 7, "file": 4, "line": 315, "parent": 21}, {"file": 8, "parent": 22}, {"command": 6, "file": 8, "line": 55, "parent": 23}, {"file": 7, "parent": 24}, {"command": 5, "file": 7, "line": 61, "parent": 25}, {"command": 7, "file": 4, "line": 297, "parent": 6}, {"file": 13, "parent": 27}, {"command": 6, "file": 13, "line": 55, "parent": 28}, {"file": 12, "parent": 29}, {"command": 5, "file": 12, "line": 61, "parent": 30}, {"command": 6, "file": 3, "line": 43, "parent": 8}, {"file": 16, "parent": 32}, {"command": 9, "file": 16, "line": 45, "parent": 33}, {"command": 8, "file": 10, "line": 145, "parent": 34}, {"command": 7, "file": 9, "line": 76, "parent": 35}, {"command": 7, "file": 4, "line": 315, "parent": 36}, {"file": 15, "parent": 37}, {"command": 6, "file": 15, "line": 55, "parent": 38}, {"file": 14, "parent": 39}, {"command": 5, "file": 14, "line": 61, "parent": 40}, {"command": 8, "file": 10, "line": 145, "parent": 19}, {"command": 7, "file": 9, "line": 76, "parent": 42}, {"command": 7, "file": 4, "line": 315, "parent": 43}, {"file": 18, "parent": 44}, {"command": 6, "file": 18, "line": 58, "parent": 45}, {"file": 17, "parent": 46}, {"command": 5, "file": 17, "line": 61, "parent": 47}, {"command": 4, "file": 0, "line": 647, "parent": 2}, {"command": 7, "file": 4, "line": 297, "parent": 6}, {"file": 20, "parent": 50}, {"command": 6, "file": 20, "line": 57, "parent": 51}, {"file": 19, "parent": 52}, {"command": 5, "file": 19, "line": 61, "parent": 53}, {"command": 6, "file": 18, "line": 46, "parent": 45}, {"file": 23, "parent": 55}, {"command": 9, "file": 23, "line": 45, "parent": 56}, {"command": 8, "file": 10, "line": 145, "parent": 57}, {"command": 7, "file": 9, "line": 76, "parent": 58}, {"command": 7, "file": 4, "line": 315, "parent": 59}, {"file": 22, "parent": 60}, {"command": 6, "file": 22, "line": 55, "parent": 61}, {"file": 21, "parent": 62}, {"command": 5, "file": 21, "line": 61, "parent": 63}, {"file": 1, "line": -1, "parent": 0}, {"command": 15, "file": 25, "line": 1, "parent": 65}, {"command": 14, "file": 0, "line": 823, "parent": 66}, {"command": 13, "file": 0, "line": 745, "parent": 67}, {"command": 12, "file": 0, "line": 745, "parent": 68}, {"command": 11, "file": 24, "line": 4361, "parent": 69}, {"command": 10, "file": 24, "line": 4136, "parent": 70}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++17 -MDd"}, {"backtrace": 49, "fragment": "-Zc:__cplusplus"}, {"backtrace": 49, "fragment": "-permissive-"}, {"backtrace": 49, "fragment": "-utf-8"}], "defines": [{"backtrace": 49, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 5, "define": "QT_OPENGL_LIB"}, {"backtrace": 5, "define": "QT_POSITIONING_LIB"}, {"backtrace": 5, "define": "QT_QMLINTEGRATION_LIB"}, {"backtrace": 5, "define": "QT_QMLMETA_LIB"}, {"backtrace": 5, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 5, "define": "QT_QMLWORKERSCRIPT_LIB"}, {"backtrace": 5, "define": "QT_QML_LIB"}, {"backtrace": 5, "define": "QT_QUICK_LIB"}, {"backtrace": 5, "define": "QT_WEBCHANNELQUICK_LIB"}, {"backtrace": 5, "define": "QT_WEBCHANNEL_LIB"}, {"backtrace": 5, "define": "QT_WEBENGINECORE_LIB"}, {"backtrace": 5, "define": "QT_WEBENGINEQUICK_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 49, "define": "UNICODE"}, {"backtrace": 49, "define": "WIN32"}, {"backtrace": 49, "define": "WIN64"}, {"backtrace": 49, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 49, "define": "_UNICODE"}, {"backtrace": 49, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "D:/Workspace/QtMarkdown/build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/QtMarkdown_autogen/include"}, {"backtrace": 49, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtCore"}, {"backtrace": 49, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include"}, {"backtrace": 49, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/mkspecs/win32-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtQuick"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtQml"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtQmlIntegration"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtQmlMeta"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtQmlModels"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtQmlWorkerScript"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtOpenGL"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtWebEngineQuick"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtWebEngineCore"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtWebChannel"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtPositioning"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/6.8.3/msvc2022_64/include/QtWebChannelQuick"}], "language": "CXX", "languageStandard": {"backtraces": [49, 49], "standard": "17"}, "sourceIndexes": [0, 1, 4]}], "dependencies": [{"backtrace": 0, "id": "QtMarkdown_autogen::@6890427a1f51a3e7e1df"}, {"backtrace": 71, "id": "QtMarkdown_qmlimportscan::@6890427a1f51a3e7e1df"}, {"id": "QtMarkdown_autogen_timestamp_deps::@6890427a1f51a3e7e1df"}], "id": "QtMarkdown::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-DQT_QML_DEBUG /DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -MDd", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6Widgetsd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6WebEngineQuickd.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6WebEngineCored.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6Quickd.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6QmlMetad.lib", "role": "libraries"}, {"backtrace": 26, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6QmlWorkerScriptd.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6QmlModelsd.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6OpenGLd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6Guid.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 31, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 16, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6WebChanneld.lib", "role": "libraries"}, {"backtrace": 41, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6Positioningd.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6WebChannelQuickd.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6Qmld.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6Networkd.lib", "role": "libraries"}, {"backtrace": 49, "fragment": "C:\\Qt\\6.8.3\\msvc2022_64\\lib\\Qt6Cored.lib", "role": "libraries"}, {"backtrace": 54, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 54, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 64, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 48, "fragment": "shell32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "QtMarkdown", "nameOnDisk": "QtMarkdown.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 4]}, {"name": "Resources", "sourceIndexes": [2]}, {"name": "", "sourceIndexes": [3]}, {"name": "CMake Rules", "sourceIndexes": [5, 6]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/QtMarkdown_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "path": "qml.qrc", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/QtMarkdown_autogen/timestamp", "sourceGroupIndex": 2}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/QtMarkdown_autogen/EWIEGA46WW/qrc_qml.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/QtMarkdown_autogen/timestamp.rule", "sourceGroupIndex": 3}, {"backtrace": 0, "isGenerated": true, "path": "build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/QtMarkdown_autogen/EWIEGA46WW/qrc_qml.cpp.rule", "sourceGroupIndex": 3}], "type": "EXECUTABLE"}