{"BUILD_DIR": "D:/Workspace/QtMarkdown/build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/QtMarkdown_autogen", "CMAKE_BINARY_DIR": "D:/Workspace/QtMarkdown/build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug", "CMAKE_CURRENT_BINARY_DIR": "D:/Workspace/QtMarkdown/build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug", "CMAKE_CURRENT_SOURCE_DIR": "D:/Workspace/QtMarkdown", "CMAKE_SOURCE_DIR": "D:/Workspace/QtMarkdown", "CROSS_CONFIG": false, "GENERATOR": "Ninja", "INCLUDE_DIR": "D:/Workspace/QtMarkdown/build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/QtMarkdown_autogen/include", "INPUTS": ["D:/Workspace/QtMarkdown/ChatMessage.qml", "D:/Workspace/QtMarkdown/markdown-template.html", "D:/Workspace/QtMarkdown/main.qml", "D:/Workspace/QtMarkdown/ChatInterface.qml"], "LOCK_FILE": "D:/Workspace/QtMarkdown/build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/CMakeFiles/QtMarkdown_autogen.dir/AutoRcc_qml_EWIEGA46WW_Lock.lock", "MULTI_CONFIG": false, "OPTIONS": ["--no-zstd", "-name", "qml"], "OUTPUT_CHECKSUM": "EWIEGA46WW", "OUTPUT_NAME": "qrc_qml.cpp", "RCC_EXECUTABLE": "C:/Qt/6.8.3/msvc2022_64/bin/rcc.exe", "RCC_LIST_OPTIONS": ["--list"], "SETTINGS_FILE": "D:/Workspace/QtMarkdown/build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/CMakeFiles/QtMarkdown_autogen.dir/AutoRcc_qml_EWIEGA46WW_Used.txt", "SOURCE": "D:/Workspace/QtMarkdown/qml.qrc", "USE_BETTER_GRAPH": true, "VERBOSITY": 0}