set(qml_import_scanner_imports_count 31)
set(qml_import_scanner_import_0 "CLASSNAME;QtQuick2Plugin;LINKTARGET;Qt6::qtquick2plugin;NAME;QtQuick;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick;PLUGIN;qtquick2plugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/;RELATIVEPATH;QtQuick;TYPE;module;")
set(qml_import_scanner_import_1 "CLASSNAME;QtQmlPlugin;LINKTARGET;Qt6::qmlplugin;NAME;QtQml;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQml;PLUGIN;qmlplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/;RELATIVEPATH;QtQml;TYPE;module;")
set(qml_import_scanner_import_2 "NAME;QML;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QML;PREFER;:/qt-project.org/imports/QML/;RELATIVEPATH;QML;TYPE;module;")
set(qml_import_scanner_import_3 "CLASSNAME;QtQmlModelsPlugin;LINKTARGET;Qt6::modelsplugin;NAME;QtQml.Models;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQml/Models;PLUGIN;modelsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/Models/;RELATIVEPATH;QtQml/Models;TYPE;module;")
set(qml_import_scanner_import_4 "CLASSNAME;QtQmlWorkerScriptPlugin;LINKTARGET;Qt6::workerscriptplugin;NAME;QtQml.WorkerScript;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQml/WorkerScript;PLUGIN;workerscriptplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQml/WorkerScript/;RELATIVEPATH;QtQml/WorkerScript;TYPE;module;")
set(qml_import_scanner_import_5 "CLASSNAME;QtQuickControls2Plugin;LINKTARGET;Qt6::qtquickcontrols2plugin;NAME;QtQuick.Controls;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls;PLUGIN;qtquickcontrols2plugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/;RELATIVEPATH;QtQuick/Controls;TYPE;module;")
set(qml_import_scanner_import_6 "CLASSNAME;QtQuickControls2FusionStylePlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ApplicationWindow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/BusyIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Button.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/CheckBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/CheckDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ComboBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/DelayButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Dial.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Dialog.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/DialogButtonBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Drawer.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Frame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/GroupBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/HorizontalHeaderView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ItemDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Label.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Menu.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/MenuBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/MenuBarItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/MenuItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/MenuSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Page.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/PageIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Pane.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Popup.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ProgressBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/RadioButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/RadioDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/RangeSlider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/RoundButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ScrollBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ScrollIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ScrollView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/SelectionRectangle.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Slider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/SpinBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/SplitView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/SwipeDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Switch.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/SwitchDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/TabBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/TabButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/TextArea.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/TextField.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ToolBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ToolButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ToolSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/ToolTip.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/TreeViewDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/Tumbler.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2fusionstyleplugin;NAME;QtQuick.Controls.Fusion;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion;PLUGIN;qtquickcontrols2fusionstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Fusion/;RELATIVEPATH;QtQuick/Controls/Fusion;TYPE;module;")
set(qml_import_scanner_import_7 "CLASSNAME;QtQuickControls2MaterialStylePlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ApplicationWindow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/BusyIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Button.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/CheckBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/CheckDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ComboBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/DelayButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Dial.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Dialog.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/DialogButtonBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Drawer.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Frame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/GroupBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/HorizontalHeaderView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ItemDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Label.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Menu.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/MenuBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/MenuBarItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/MenuItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/MenuSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Page.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/PageIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Pane.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Popup.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ProgressBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/RadioButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/RadioDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/RangeSlider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/RoundButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ScrollBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ScrollIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ScrollView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/SelectionRectangle.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Slider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/SpinBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/SplitView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/StackView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/SwipeDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/SwipeView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Switch.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/SwitchDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/TabBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/TabButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/TextArea.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/TextField.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ToolBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ToolButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ToolSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/ToolTip.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/TreeViewDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/Tumbler.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2materialstyleplugin;NAME;QtQuick.Controls.Material;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material;PLUGIN;qtquickcontrols2materialstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Material/;RELATIVEPATH;QtQuick/Controls/Material;TYPE;module;")
set(qml_import_scanner_import_8 "CLASSNAME;QtQuickControls2ImagineStylePlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ApplicationWindow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/BusyIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Button.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/CheckBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/CheckDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ComboBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/DelayButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Dial.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Dialog.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/DialogButtonBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Drawer.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Frame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/GroupBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/HorizontalHeaderView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ItemDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Label.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Menu.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/MenuItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/MenuSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Page.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/PageIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Pane.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Popup.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ProgressBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/RadioButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/RadioDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/RangeSlider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/RoundButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ScrollBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ScrollIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ScrollView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/SelectionRectangle.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Slider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/SpinBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/SplitView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/StackView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/SwipeDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/SwipeView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Switch.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/SwitchDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/TabBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/TabButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/TextArea.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/TextField.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ToolBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ToolButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ToolSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/ToolTip.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/Tumbler.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2imaginestyleplugin;NAME;QtQuick.Controls.Imagine;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine;PLUGIN;qtquickcontrols2imaginestyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Imagine/;RELATIVEPATH;QtQuick/Controls/Imagine;TYPE;module;")
set(qml_import_scanner_import_9 "CLASSNAME;QtQuickControls2UniversalStylePlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ApplicationWindow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/BusyIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Button.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/CheckBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/CheckDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ComboBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/DelayButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Dial.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Dialog.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/DialogButtonBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Drawer.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Frame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/GroupBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/HorizontalHeaderView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ItemDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Label.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Menu.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/MenuBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/MenuBarItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/MenuItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/MenuSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Page.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/PageIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Pane.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Popup.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ProgressBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/RadioButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/RadioDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/RangeSlider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/RoundButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ScrollBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ScrollIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ScrollView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/SelectionRectangle.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Slider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/SpinBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/SplitView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/StackView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/SwipeDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Switch.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/SwitchDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/TabBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/TabButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/TextArea.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/TextField.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ToolBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ToolButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ToolSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/ToolTip.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/Tumbler.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/VerticalHeaderView.qml;LINKTARGET;Qt6::qtquickcontrols2universalstyleplugin;NAME;QtQuick.Controls.Universal;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal;PLUGIN;qtquickcontrols2universalstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Universal/;RELATIVEPATH;QtQuick/Controls/Universal;TYPE;module;")
set(qml_import_scanner_import_10 "CLASSNAME;QtQuickControls2FluentWinUI3StylePlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/ApplicationWindow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/BusyIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/Button.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/CheckBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/CheckDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/ComboBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/Config.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/DelayButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/Dialog.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/DialogButtonBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/FocusFrame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/Frame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/GroupBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/ItemDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/Menu.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/MenuBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/MenuBarItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/MenuItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/MenuSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/PageIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/Popup.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/ProgressBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/RadioButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/RadioDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/RangeSlider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/RoundButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/Slider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/SpinBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/StyleImage.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/SwipeDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/Switch.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/SwitchDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/TabBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/TabButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/TextArea.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/TextField.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/ToolBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/ToolButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/ToolSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/ToolTip.qml;LINKTARGET;Qt6::qtquickcontrols2fluentwinui3styleplugin;NAME;QtQuick.Controls.FluentWinUI3;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3;PLUGIN;qtquickcontrols2fluentwinui3styleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/FluentWinUI3/;RELATIVEPATH;QtQuick/Controls/FluentWinUI3;TYPE;module;")
set(qml_import_scanner_import_11 "CLASSNAME;QtQuickControls2WindowsStylePlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/ApplicationWindow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/Button.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/CheckBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/CheckDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/ComboBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/DelayButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/Frame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/GroupBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/ItemDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/Menu.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/MenuBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/MenuBarItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/MenuItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/MenuSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/ProgressBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/RadioButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/RadioDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/RangeSlider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/ScrollBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/ScrollIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/ScrollView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/SelectionRectangle.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/Slider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/SpinBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/Switch.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/SwitchDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/TextArea.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/TextField.qml;LINKTARGET;Qt6::qtquickcontrols2windowsstyleplugin;NAME;QtQuick.Controls.Windows;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows;PLUGIN;qtquickcontrols2windowsstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Windows/;RELATIVEPATH;QtQuick/Controls/Windows;TYPE;module;")
set(qml_import_scanner_import_12 "NAME;QtQuick.Controls.macOS;TYPE;module;")
set(qml_import_scanner_import_13 "NAME;QtQuick.Controls.iOS;TYPE;module;")
set(qml_import_scanner_import_14 "CLASSNAME;QtQuickControls2BasicStylePlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/AbstractButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Action.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ActionGroup.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ApplicationWindow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/BusyIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Button.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ButtonGroup.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Calendar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/CalendarModel.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/CheckBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/CheckDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ComboBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Container.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Control.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/DayOfWeekRow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/DelayButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Dial.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Dialog.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/DialogButtonBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Drawer.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Frame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/GroupBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/HorizontalHeaderView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ItemDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Label.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Menu.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/MenuBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/MenuBarItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/MenuItem.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/MenuSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/MonthGrid.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Page.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/PageIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Pane.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Popup.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ProgressBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/RadioButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/RadioDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/RangeSlider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/RoundButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ScrollBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ScrollIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ScrollView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/SelectionRectangle.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Slider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/SpinBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/SplitView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/StackView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/SwipeDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/SwipeView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Switch.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/SwitchDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/TabBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/TabButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/TextArea.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/TextField.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ToolBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ToolButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ToolSeparator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/ToolTip.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/TreeViewDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/Tumbler.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/VerticalHeaderView.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/WeekNumberColumn.qml;LINKTARGET;Qt6::qtquickcontrols2basicstyleplugin;NAME;QtQuick.Controls.Basic;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic;PLUGIN;qtquickcontrols2basicstyleplugin;PREFER;:/qt-project.org/imports/QtQuick/Controls/Basic/;RELATIVEPATH;QtQuick/Controls/Basic;TYPE;module;")
set(qml_import_scanner_import_15 "CLASSNAME;QtQuickTemplates2Plugin;LINKTARGET;Qt6::qtquicktemplates2plugin;NAME;QtQuick.Templates;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Templates;PLUGIN;qtquicktemplates2plugin;PREFER;:/qt-project.org/imports/QtQuick/Templates/;RELATIVEPATH;QtQuick/Templates;TYPE;module;")
set(qml_import_scanner_import_16 "CLASSNAME;QtQuickControls2ImplPlugin;LINKTARGET;Qt6::qtquickcontrols2implplugin;NAME;QtQuick.Controls.impl;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/impl;PLUGIN;qtquickcontrols2implplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/impl/;RELATIVEPATH;QtQuick/Controls/impl;TYPE;module;")
set(qml_import_scanner_import_17 "CLASSNAME;QtQuickControls2FusionStyleImplPlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/impl/ButtonPanel.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/impl/CheckIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/impl/RadioIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/impl/SliderGroove.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/impl/SliderHandle.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2fusionstyleimplplugin;NAME;QtQuick.Controls.Fusion.impl;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Fusion/impl;PLUGIN;qtquickcontrols2fusionstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Fusion/impl/;RELATIVEPATH;QtQuick/Controls/Fusion/impl;TYPE;module;")
set(qml_import_scanner_import_18 "CLASSNAME;QtQuick_WindowPlugin;LINKTARGET;Qt6::quickwindow;NAME;QtQuick.Window;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Window;PLUGIN;quickwindowplugin;PREFER;:/qt-project.org/imports/QtQuick/Window/;RELATIVEPATH;QtQuick/Window;TYPE;module;")
set(qml_import_scanner_import_19 "CLASSNAME;QtQuickControls2MaterialStyleImplPlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/BoxShadow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/CheckIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/CursorDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/ElevationEffect.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/RadioIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/RectangularGlow.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/RoundedElevationEffect.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/SliderHandle.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2materialstyleimplplugin;NAME;QtQuick.Controls.Material.impl;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Material/impl;PLUGIN;qtquickcontrols2materialstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Material/impl/;RELATIVEPATH;QtQuick/Controls/Material/impl;TYPE;module;")
set(qml_import_scanner_import_20 "CLASSNAME;QtQuickControls2ImagineStyleImplPlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/impl/OpacityMask.qml;LINKTARGET;Qt6::qtquickcontrols2imaginestyleimplplugin;NAME;QtQuick.Controls.Imagine.impl;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Imagine/impl;PLUGIN;qtquickcontrols2imaginestyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Imagine/impl/;RELATIVEPATH;QtQuick/Controls/Imagine/impl;TYPE;module;")
set(qml_import_scanner_import_21 "CLASSNAME;QtQuickControls2UniversalStyleImplPlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/impl/CheckIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/impl/RadioIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2universalstyleimplplugin;NAME;QtQuick.Controls.Universal.impl;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Universal/impl;PLUGIN;qtquickcontrols2universalstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Universal/impl/;RELATIVEPATH;QtQuick/Controls/Universal/impl;TYPE;module;")
set(qml_import_scanner_import_22 "CLASSNAME;QtQuickControls2FluentWinUI3StyleImplPlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/impl/ButtonBackground.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/impl/CheckIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/impl/RadioIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2fluentwinui3styleimplplugin;NAME;QtQuick.Controls.FluentWinUI3.impl;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/FluentWinUI3/impl;PLUGIN;qtquickcontrols2fluentwinui3styleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/FluentWinUI3/impl/;RELATIVEPATH;QtQuick/Controls/FluentWinUI3/impl;TYPE;module;")
set(qml_import_scanner_import_23 "CLASSNAME;QtQuickEffectsPlugin;LINKTARGET;Qt6::effectsplugin;NAME;QtQuick.Effects;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Effects;PLUGIN;effectsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Effects/;RELATIVEPATH;QtQuick/Effects;TYPE;module;")
set(qml_import_scanner_import_24 "CLASSNAME;QtQuickLayoutsPlugin;LINKTARGET;Qt6::qquicklayoutsplugin;NAME;QtQuick.Layouts;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Layouts;PLUGIN;qquicklayoutsplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Layouts/;RELATIVEPATH;QtQuick/Layouts;TYPE;module;")
set(qml_import_scanner_import_25 "CLASSNAME;QmlShapesPlugin;LINKTARGET;Qt6::qmlshapesplugin;NAME;QtQuick.Shapes;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Shapes;PLUGIN;qmlshapesplugin;PREFER;:/qt-project.org/imports/QtQuick/Shapes/;RELATIVEPATH;QtQuick/Shapes;TYPE;module;")
set(qml_import_scanner_import_26 "CLASSNAME;QtQuickControls2NativeStylePlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultCheckBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultComboBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultDial.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultFrame.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultGroupBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultItemDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultItemDelegateIconLabel.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultProgressBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultRadioButton.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultRadioDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultScrollBar.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultSlider.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultSpinBox.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultTextArea.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultTextField.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/controls/DefaultTreeViewDelegate.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle/util/WindowsFocusFrame.qml;LINKTARGET;Qt6::qtquickcontrols2nativestyleplugin;NAME;QtQuick.NativeStyle;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/NativeStyle;PLUGIN;qtquickcontrols2nativestyleplugin;PREFER;:/qt-project.org/imports/QtQuick/NativeStyle/;RELATIVEPATH;QtQuick/NativeStyle;TYPE;module;")
set(qml_import_scanner_import_27 "CLASSNAME;QtQuickControls2WindowsStyleImplPlugin;COMPONENTS;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/impl/CheckIndicator.qml;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/impl/SwitchIndicator.qml;LINKTARGET;Qt6::qtquickcontrols2windowsstyleimplplugin;NAME;QtQuick.Controls.Windows.impl;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Windows/impl;PLUGIN;qtquickcontrols2windowsstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Windows/impl/;RELATIVEPATH;QtQuick/Controls/Windows/impl;TYPE;module;")
set(qml_import_scanner_import_28 "CLASSNAME;QtQuickControls2BasicStyleImplPlugin;LINKTARGET;Qt6::qtquickcontrols2basicstyleimplplugin;NAME;QtQuick.Controls.Basic.impl;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtQuick/Controls/Basic/impl;PLUGIN;qtquickcontrols2basicstyleimplplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtQuick/Controls/Basic/impl/;RELATIVEPATH;QtQuick/Controls/Basic/impl;TYPE;module;")
set(qml_import_scanner_import_29 "CLASSNAME;QtWebEnginePlugin;LINKTARGET;Qt6::qtwebenginequickplugin;NAME;QtWebEngine;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtWebEngine;PLUGIN;qtwebenginequickplugin;PREFER;:/qt-project.org/imports/QtWebEngine/;RELATIVEPATH;QtWebEngine;TYPE;module;")
set(qml_import_scanner_import_30 "CLASSNAME;QtWebChannelPlugin;LINKTARGET;Qt6::WebChannelQuickplugin;NAME;QtWebChannel;PATH;C:/Qt/6.8.3/msvc2022_64/qml/QtWebChannel;PLUGIN;webchannelquickplugin;PLUGINISOPTIONAL;;PREFER;:/qt-project.org/imports/QtWebChannel/;RELATIVEPATH;QtWebChannel;TYPE;module;")

