[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "--driver-mode=cl", "-DQT_QML_DEBUG", "-DWIN32", "-D_WINDOWS", "-GR", "-EHsc", "-<PERSON>i", "-RTC1", "-clang:-std=c++17", "-MDd", "-Zc:__cplusplus", "-permissive-", "-utf-8", "/Zs", "-m64", "--target=x86_64-pc-windows-msvc", "-fcxx-exceptions", "-fexceptions", "-fms-compatibility-version=19.43", "-DQT_CORE_LIB", "-DQT_GUI_LIB", "-DQT_NETWORK_LIB", "-DQT_OPENGL_LIB", "-DQT_POSITIONING_LIB", "-DQT_QMLINTEGRATION_LIB", "-DQT_QMLMETA_LIB", "-DQT_QMLMODELS_LIB", "-DQT_QMLWORKERSCRIPT_LIB", "-DQT_QML_LIB", "-DQT_QUICK_LIB", "-DQT_WEBCHANNELQUICK_LIB", "-DQT_WEBCHANNEL_LIB", "-DQT_WEBENGINECORE_LIB", "-DQT_WEBENGINEQUICK_LIB", "-DQT_WIDGETS_LIB", "-DUNICODE", "-DWIN32", "-DWIN64", "-D_ENABLE_EXTENDED_ALIGNED_STORAGE", "-D_UNICODE", "-D_WIN64", "-DQ_CREATOR_RUN", "-D__FUNCSIG__=\"void __cdecl someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580(void)\"", "-D__FUNCTION__=\"someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580\"", "-D__FUNCDNAME__=\"?someLegalAndLongishFunctionNameThatWorksAroundQTCREATORBUG-24580@@YAXXZ\"", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders", "-IC:\\Qt\\Tools\\QtCreator\\share\\qtcreator\\cplusplus\\wrappedQtHeaders\\QtCore", "-ID:\\Workspace\\QtMarkdown\\build\\Desktop_Qt_6_8_3_MSVC2022_64bit-Debug\\QtMarkdown_autogen\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtCore", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\mkspecs\\win32-msvc", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtGui", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtWidgets", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtQml", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtQmlIntegration", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtNetwork", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtQmlMeta", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtQmlModels", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtQmlWorkerScript", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtOpenGL", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtWebEngineQuick", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtWebEngineCore", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtWebChannel", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtPositioning", "/clang:-isystem", "/clang:C:\\Qt\\6.8.3\\msvc2022_64\\include\\QtWebChannelQuick", "/clang:-isystem", "/clang:C:\\Qt\\Tools\\QtCreator\\bin\\clang\\lib\\clang\\20\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.43.34808\\ATLMFC\\include", "/clang:-isystem", "/clang:C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Auxiliary\\VS\\include", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\ucrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\um", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\shared", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\winrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\10\\include\\10.0.22621.0\\cppwinrt", "/clang:-isystem", "/clang:C:\\Program Files (x86)\\Windows Kits\\NETFXSDK\\4.8\\include\\um", "/clang:-fmessage-length=0", "/clang:-fdiagnostics-show-note-include-stack", "/clang:-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "/TP", "D:\\Workspace\\QtMarkdown\\main.cpp"], "directory": "D:/Workspace/QtMarkdown/build/Desktop_Qt_6_8_3_MSVC2022_64bit-Debug/.qtc_clangd", "file": "D:/Workspace/QtMarkdown/main.cpp"}]