import QtQuick 2.15
import QtQuick.Controls
import QtQuick.Layouts
import QtWebEngine
import QtWebChannel

Item {
    id: root

    property bool isUserMessage: false
    property string messageText: ""
    property string timeStamp: ""

    height: messageContainer.height + 30

    // WebChannel 对象用于与 HTML 页面通信
    QtObject {
        id: messageHandler
        WebChannel.id: "messageHandler"

        function linkClicked(url) {
            Qt.openUrlExternally(url)
        }
    }

    WebChannel {
        id: webChannel
        registeredObjects: [messageHandler]
    }

    RowLayout {
        id: messageContainer
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.topMargin: 15
        anchors.leftMargin: 10
        anchors.rightMargin: 10
        spacing: 15
        
        // 用户消息：右对齐，bot消息：左对齐
        Item {
            Layout.fillWidth: !isUserMessage
            Layout.preferredWidth: isUserMessage ? parent.width * 0.3 : 0
        }
        
        // 头像
        Rectangle {
            Layout.preferredWidth: 40
            Layout.preferredHeight: 40
            Layout.alignment: Qt.AlignTop
            radius: 20
            color: isUserMessage ? "#4CAF50" : "#2196F3"
            
            Text {
                anchors.centerIn: parent
                text: isUserMessage ? "U" : "AI"
                color: "white"
                font.pixelSize: 16
                font.weight: Font.Bold
            }
        }
        
        // 消息内容
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredWidth: parent.width * 0.7
            Layout.preferredHeight: Math.max(webView.height + timeStampText.height + 36, 80)
            color: isUserMessage ? "#f0f9ff" : "#f8f9fa"
            border.color: isUserMessage ? "#e0f2fe" : "#e9ecef"
            border.width: 1
            radius: 12

            ColumnLayout {
                id: messageColumn
                anchors.fill: parent
                anchors.margins: 18
                spacing: 12

                // 使用 WebEngineView 渲染 Markdown
                WebEngineView {
                    id: webView
                    Layout.fillWidth: true
                    Layout.preferredHeight: contentHeight
                    Layout.minimumHeight: 40

                    webChannel: root.webChannel

                    property int contentHeight: 60

                    url: "qrc:/markdown-template.html"

                    onLoadingChanged: function(loadRequest) {
                        if (loadRequest.status === WebEngineView.LoadSucceededStatus) {
                            updateContent()
                        }
                    }

                    function updateContent() {
                        if (messageText) {
                            // 设置背景色与气泡框一致
                            var bgColor = isUserMessage ? "#f0f9ff" : "#f8f9fa"
                            var setBgScript = "document.body.style.backgroundColor = '" + bgColor + "';"
                            runJavaScript(setBgScript)

                            // 转义 JavaScript 字符串中的特殊字符
                            var escapedText = messageText
                                .replace(/\\/g, '\\\\')
                                .replace(/'/g, "\\'")
                                .replace(/"/g, '\\"')
                                .replace(/\n/g, '\\n')
                                .replace(/\r/g, '\\r')
                                .replace(/\t/g, '\\t')

                            var script = "window.setMarkdownContent('" + escapedText + "');"
                            runJavaScript(script)

                            // 延迟获取实际内容高度
                            Qt.callLater(function() {
                                var heightScript = "Math.max(40, document.body.scrollHeight);"
                                runJavaScript(heightScript, function(result) {
                                    if (typeof result === 'number' && result > 0) {
                                        contentHeight = result
                                    }
                                })
                            })
                        }
                    }

                    // 当消息文本改变时更新内容
                    Connections {
                        target: root
                        function onMessageTextChanged() {
                            if (webView.loading === false) {
                                webView.updateContent()
                            }
                        }
                    }
                }

                // 时间戳
                Text {
                    id: timeStampText
                    Layout.fillWidth: true
                    Layout.alignment: isUserMessage ? Qt.AlignRight : Qt.AlignLeft
                    text: timeStamp
                    font.pixelSize: 12
                    color: "#888"
                    horizontalAlignment: isUserMessage ? Text.AlignRight : Text.AlignLeft
                }
            }
        }
        
        // 用户消息：左侧空白，bot消息：右侧空白
        Item {
            Layout.fillWidth: isUserMessage
            Layout.preferredWidth: !isUserMessage ? parent.width * 0.3 : 0
        }
    }

}
