import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "MarkdownRenderer.js" as MarkdownRenderer

Item {
    id: root

    property bool isUserMessage: false
    property string messageText: ""
    property string timeStamp: ""

    height: messageContainer.height + 30

    RowLayout {
        id: messageContainer
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.topMargin: 15
        anchors.leftMargin: 10
        anchors.rightMargin: 10
        spacing: 15
        
        // 用户消息：右对齐，bot消息：左对齐
        Item {
            Layout.fillWidth: !isUserMessage
            Layout.preferredWidth: isUserMessage ? parent.width * 0.3 : 0
        }
        
        // 头像
        Rectangle {
            Layout.preferredWidth: 40
            Layout.preferredHeight: 40
            Layout.alignment: Qt.AlignTop
            radius: 20
            color: isUserMessage ? "#4CAF50" : "#2196F3"
            
            Text {
                anchors.centerIn: parent
                text: isUserMessage ? "U" : "AI"
                color: "white"
                font.pixelSize: 16
                font.weight: Font.Bold
            }
        }
        
        // 消息内容
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredWidth: parent.width * 0.7
            Layout.preferredHeight: messageColumn.implicitHeight + 36
            color: isUserMessage ? "#f0f9ff" : "#f8f9fa"
            border.color: isUserMessage ? "#e0f2fe" : "#e9ecef"
            border.width: 1
            radius: 12

            ColumnLayout {
                id: messageColumn
                anchors.fill: parent
                anchors.margins: 18
                spacing: 12

                // 消息文本 - 使用改进的 Markdown 渲染
                TextArea {
                    id: messageContent
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(implicitHeight, 30)
                    text: MarkdownRenderer.renderMarkdown(messageText)
                    textFormat: Text.RichText
                    wrapMode: TextArea.Wrap
                    readOnly: true
                    selectByMouse: true
                    font.pixelSize: 14
                    font.family: "Segoe UI, Arial, sans-serif"
                    color: "#333"

                    background: Rectangle {
                        color: "transparent"
                    }

                    // 处理链接点击
                    onLinkActivated: function(link) {
                        Qt.openUrlExternally(link)
                    }
                }

                // 时间戳
                Text {
                    Layout.fillWidth: true
                    Layout.alignment: isUserMessage ? Qt.AlignRight : Qt.AlignLeft
                    text: timeStamp
                    font.pixelSize: 12
                    color: "#888"
                    horizontalAlignment: isUserMessage ? Text.AlignRight : Text.AlignLeft
                }
            }
        }
        
        // 用户消息：左侧空白，bot消息：右侧空白
        Item {
            Layout.fillWidth: isUserMessage
            Layout.preferredWidth: !isUserMessage ? parent.width * 0.3 : 0
        }
    }

}
